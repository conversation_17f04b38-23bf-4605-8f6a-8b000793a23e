<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7b6bcee8-0888-4868-82e7-a5fbeff7e90c" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/../cadf-scheduler/test/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../cadf-scheduler/scheduler/spider/account_traffic_spider_scheduler.py" beforeDir="false" afterPath="$PROJECT_DIR$/../cadf-scheduler/scheduler/spider/account_traffic_spider_scheduler.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zcKmggtk6yyYhKYsFlGuJ5KJrT" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.run_browser_detection_test.executor": "Run",
    "Python.run_xhs_accounts_crawl_consumer.executor": "Debug",
    "Python.run_xhs_note_crawl_consumer.executor": "Debug",
    "Python.start_all.executor": "Debug",
    "Python.test_account_traffic_metrics_spider.executor": "Run",
    "Python.test_note_comment_spider.executor": "Run",
    "Python.test_note_content_spider.executor": "Run",
    "Python.test_xhs_note_content_spider.executor": "Run",
    "Python.xhs_login.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/project/cyber-ad-factory/cadf-consumer-agent-simple",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\common-module\omni\exception" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-consumer-spider-xhs\spider\tools" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-consumer-spider-xhs\spider\xhs_playwright" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-consumer-spider-xhs\consumers" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="start_all" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="cadf-consumer-spider-xhs" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/start_all.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.start_all" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.26574.90" />
        <option value="bundled-python-sdk-c1fac28bca04-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.26574.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="7b6bcee8-0888-4868-82e7-a5fbeff7e90c" name="更改" comment="" />
      <created>1752025082067</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752025082067</updated>
      <workItem from="1752025083188" duration="1088000" />
      <workItem from="1752028357066" duration="12130000" />
      <workItem from="*************" duration="490000" />
      <workItem from="*************" duration="********" />
      <workItem from="*************" duration="2363000" />
      <workItem from="*************" duration="6705000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$run_xhs_accounts_crawl_consumer.coverage" NAME="run_xhs_accounts_crawl_consumer 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/runners" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$test_xhs_note_content_spider.coverage" NAME="test_xhs_note_content_spider 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/runners" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$run_browser_detection_test.coverage" NAME="run_browser_detection_test 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/runners" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$run_xhs_note_crawl_consumer.coverage" NAME="run_xhs_note_crawl_consumer 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/runners" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$test_account_traffic_metrics_spider.coverage" NAME="test_account_traffic_metrics_spider 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$start_all.coverage" NAME="start_all 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$test_note_comment_spider.coverage" NAME="test_note_comment_spider 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$test_note_content_spider.coverage" NAME="test_note_content_spider 覆盖结果" MODIFIED="1752346877809" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/cadf_consumer_spider_xhs$xhs_login.coverage" NAME="xhs_login 覆盖结果" MODIFIED="1752320468999" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/runners" />
  </component>
</project>