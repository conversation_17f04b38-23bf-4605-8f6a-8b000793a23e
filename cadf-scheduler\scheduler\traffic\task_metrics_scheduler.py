from models.models import PromotionTaskDetail
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    处理推广任务指标更新
    """
    pipeline = [
        {"$match": {"validation_status": "成功", "account_id": {"$ne": None}}},
        {
            "$lookup": {
                "from": "ai_generated_material",
                "localField": "ai_generated_material_id",
                "foreignField": "_id",
                "as": "material"
            }
        },
        {
            "$lookup": {
                "from": "account",
                "localField": "account_id",
                "foreignField": "_id",
                "as": "account"
            }
        },
        {"$match": {"material": {"$ne": []}, "account": {"$ne": []}}},
        {"$unwind": "$material"},
        {"$unwind": "$account"},
        {
            "$lookup": {
                "from": "account_metrics",
                "let": {
                    "account_id": {"$toString": "$account_id"},
                    "platform": "$account.platform",
                    "title": "$material.title"
                },
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$account_id", "$$account_id"]},
                                    {"$eq": ["$platform", "$$platform"]},
                                    {"$eq": ["$title", "$$title"]}
                                ]
                            }
                        }
                    },
                    {"$sort": {"crawled_at": -1}},
                    {"$limit": 1}
                ],
                "as": "metrics"
            }
        },
        {"$project": {"_id": 1, "metrics": {"$arrayElemAt": ["$metrics", 0]}}}
    ]
    results = await PromotionTaskDetail.aggregate(pipeline).to_list()
    updated_count = 0
    skipped_count = 0
    for result in results:
        try:
            if result.get("metrics"):
                metrics = result["metrics"]
                update_fields = {
                    "set__view_count": metrics.get("view_count", 0),
                    "set__like_count": metrics.get("like_count", 0),
                    "set__comment_count": metrics.get("comment_count", 0),
                    "set__favorite_count": metrics.get("favorite_count", 0),
                    "set__share_count": metrics.get("share_count", 0)
                }
                await PromotionTaskDetail.find_one(
                    PromotionTaskDetail.id == result["_id"]
                ).modify(**update_fields)
                olog.debug(f"已更新 PromotionTaskDetail (ID: {result['_id']}) 指标")
                updated_count += 1
            else:
                olog.warning(
                    f"找不到与 PromotionTaskDetail (ID: {result['_id']}) 匹配的 AccountTrafficMetrics 记录"
                )
                skipped_count += 1
        except Exception:
            olog.exception(f"处理 PromotionTaskDetail (ID: {result['_id']}) 时出错")
            skipped_count += 1
    olog.info(
    f"推广任务指标更新任务完成。更新: {updated_count}, 跳过/失败: {skipped_count}"
    )


@register_scheduler(trigger='cron', hour='1-23/2', minute='0')
class TaskMetricsScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行推广任务指标更新任务
        
        调度频率：每2小时执行一次（1-23点的奇数小时）
        """
        olog.info("开始执行推广任务指标更新任务")
        await execute_task()
        olog.info("推广任务指标更新任务执行完毕")


if __name__ == "__main__":
    import asyncio
    
    async def main():
        await init_models()
        await execute_task()
    
    asyncio.run(main())
