import time
from datetime import datetime
from typing import Any, Dict, Coroutine

from beanie import PydanticObjectId

from common_config.common_config import RedisKeyConfig
from models.models import (
    AiGeneratedMaterial,
    PromotionTaskDetail,
    Product,
    PromotionTask,
    Account
)
from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.api.pageable import PageResponse
from omni.integration.oss.tencent_oss import oss_client
from omni.log.log import olog
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set
from utils.platform_judgment_tool import extract_url_from_text

DAILY_TASK_LIMIT = 2
MAX_UNCOMPLETED_TASKS = 2


@register_handler('pub_promotion_task')
class PubPromotionTaskApi:

    @auth_required(['creator', 'admin'])
    async def accept_task(self, data: Dict[str, Any]):
        user_id = data.get('user_id')
        promotion_task_detail_id = data.get('promotion_task_detail_id')
        account_id = data.get('account_id')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        if not promotion_task_detail_id:
            raise MException("promotion_task_detail_id 不能为空")
        if not account_id:
            raise MException("account_id 不能为空")

        account_for_accept = await Account.find_one(Account.id == PydanticObjectId(account_id),
                                                    Account.user_id == str(user_id))
        if not account_for_accept:
            raise MException(f"账户 ID '{account_id}' 不存在或不属于您")
        if account_for_accept.status != '在线':
            raise MException(f"账户 '{account_for_accept.name}' 当前状态为 {account_for_accept.status}，无法接取任务")

        now_local_for_limits = datetime.now()
        start_of_day_local_for_limits = datetime.combine(now_local_for_limits.date(), datetime.min.time())
        start_ts_for_limits = int(start_of_day_local_for_limits.timestamp())

        accepted_today_for_limits = await PromotionTaskDetail.find(
            PromotionTaskDetail.account_id == str(account_for_accept.id),
            PromotionTaskDetail.accepted_at >= start_ts_for_limits
        ).count()
        uncompleted_for_limits = await PromotionTaskDetail.find(
            PromotionTaskDetail.account_id == str(account_for_accept.id),
            PromotionTaskDetail.validation_status != '成功'
        ).count()

        if uncompleted_for_limits >= MAX_UNCOMPLETED_TASKS:
            raise MException(
                f"账户 '{account_for_accept.name}' 当前有 {uncompleted_for_limits} 个未完成的任务 (验证状态非'成功')，达到上限 ({MAX_UNCOMPLETED_TASKS}个)，请等待任务验证成功后再接取新任务")

        if accepted_today_for_limits >= DAILY_TASK_LIMIT:
            raise MException(
                f"账户 '{account_for_accept.name}' 今日已接取 {accepted_today_for_limits} 个任务，达到每日上限 ({DAILY_TASK_LIMIT}个)")

        task_to_accept = await PromotionTaskDetail.find_one(
            PromotionTaskDetail.id == PydanticObjectId(promotion_task_detail_id),
            PromotionTaskDetail.user_id == None
        )
        if not task_to_accept:
            raise MException("任务不存在或已被接取")

        await task_to_accept.update({
            "$set": {
                PromotionTaskDetail.user_id: str(user_id),
                PromotionTaskDetail.account_id: str(account_id),
                PromotionTaskDetail.accepted_at: int(time.time()),
                PromotionTaskDetail.validation_status: '待验证',
                PromotionTaskDetail.validation_details: None,
            }
        })

    @auth_required(['creator', 'admin'])
    async def give_up_task(self, data: Dict[str, Any]):
        user_id = data.get('user_id')
        promotion_task_detail_id = data.get('promotion_task_detail_id')

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        if not promotion_task_detail_id:
            raise MException("promotion_task_detail_id 不能为空")

        task_to_give_up = await PromotionTaskDetail.find_one(
            PromotionTaskDetail.id == PydanticObjectId(promotion_task_detail_id),
            PromotionTaskDetail.user_id == str(user_id)
        )

        if not task_to_give_up:
            raise MException("无法找到您名下指定的任务，或任务状态已更新")

        if task_to_give_up.validation_status == '成功':
            raise MException("已验证成功的任务不能放弃。")

        await task_to_give_up.update({
            "$unset": {
                PromotionTaskDetail.user_id: "",
                PromotionTaskDetail.account_id: "",
                PromotionTaskDetail.accepted_at: "",
                PromotionTaskDetail.publish_url: "",
                PromotionTaskDetail.publish_at: "",
                PromotionTaskDetail.validation_status: "",
                PromotionTaskDetail.validation_details: ""
            }
        })

    @auth_required(['creator', 'admin'])
    async def modify_publish_link(self, data: Dict[str, Any]):
        user_id = data.get('user_id')
        task_detail_id = data.get('promotion_task_detail_id')
        new_link = data.get('publish_link', '').strip()

        # 输入验证
        if not user_id:
            raise MException("user_id 不能为空")
        if not task_detail_id:
            raise MException("promotion_task_detail_id 不能为空")

        if not new_link:
            raise MException("发布链接不能为空")

        task_detail = await PromotionTaskDetail.find_one(
            PromotionTaskDetail.id == PydanticObjectId(task_detail_id),
            PromotionTaskDetail.user_id == str(user_id)
        )
        if not task_detail:
            raise MException("任务不存在或您无权修改")

        # 获取账户信息以确定平台类型
        account = await Account.find_one(Account.id == PydanticObjectId(task_detail.account_id))
        if not account:
            raise MException("关联的账户不存在")
        
        platform_name = account.platform
        if not platform_name:
            raise MException("账户未设置平台信息")

        url_from_text = extract_url_from_text(new_link, platform_name)
        if not url_from_text:
            raise MException("未能在输入中提取到有效的URL，请检查链接格式")

        task_detail.publish_url = url_from_text
        task_detail.validation_status = '待验证'
        await task_detail.save()

        await publish_messages_to_redis_set(RedisKeyConfig.XHS_NOTE_URL_VERIFY_SET, {"id_": task_detail_id})
        olog.info(f"用户 {user_id} 成功更新任务 {task_detail_id} 的发布链接为: {url_from_text}")

    @auth_required(['creator', 'admin'])
    async def query_accounts_with_task_status(self, data: Dict[str, Any]) -> list[dict[str, Any]]:
        """
        查询指定用户下的账户列表，并附加每个账户的任务接取状态（今日已接、未完成）和限制信息，方便前端展示账户的当前负荷及上限。
        """
        user_id = data.get('user_id')

        now = datetime.now()
        start_of_day = datetime.combine(now.date(), datetime.min.time())
        start_ts = int(start_of_day.timestamp())

        pipeline = [
            {'$match': {'user_id': user_id}},
            {'$sort': {'create_at': -1}},
            {
                '$addFields': {
                    'account_id_str': {'$toString': '$_id'}
                }
            },
            {
                '$lookup': {
                    'from': 'promotion_task_detail',
                    'localField': 'account_id_str',
                    'foreignField': 'account_id',
                    'as': 'tasks'
                }
            },
            {
                '$addFields': {
                    'accepted_today': {
                        '$size': {
                            '$filter': {
                                'input': '$tasks',
                                'as': 'task',
                                'cond': {'$gte': ['$$task.accepted_at', start_ts]}
                            }
                        }
                    },
                    'uncompleted_tasks_count': {
                        '$size': {
                            '$filter': {
                                'input': '$tasks',
                                'as': 'task',
                                'cond': {'$ne': ['$$task.validation_status', '成功']}
                            }
                        }
                    }
                }
            },
            {
                '$project': {
                    'tasks': 0  # 移除 tasks 字段
                }
            }
        ]

        accounts_with_status = await Account.aggregate(pipeline).to_list()
        # 为结果补充 model 定义的字段并转换 ObjectId
        for acc in accounts_with_status:
            acc['_id'] = str(acc['_id'])  # 转换 ObjectId 为字符串
            acc['limit'] = DAILY_TASK_LIMIT
            acc['max_uncompleted_limit'] = MAX_UNCOMPLETED_TASKS

        return accounts_with_status

    @auth_required(['creator', 'admin'])
    async def query_market_tasks(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        """
        为指定账户查询任务市场中符合条件的推广任务列表。
        """
        user_id = data.get('user_id')
        account_id = data.get('account_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 10)

        account = await Account.find_one(Account.id == PydanticObjectId(account_id), Account.user_id == str(user_id))
        if not account:
            raise MException(f"账户 ID '{account_id}' 不存在或不属于您")
        if account.status != '在线':
            raise MException(f"账户 '{account.name}' 当前状态为 {account.status}，无法接取任务")

        preferred_domain = account.domain
        preferred_platform = account.platform

        if not preferred_platform:
            olog.exception(f"Account {account_id} does not have a preferred platform set.")
            raise MException("账户未设置推广平台，无法查询适用任务。")

        base_pipeline = [
            # 1. 匹配未分配的任务
            {'$match': {'user_id': None}},

            # 2. 添加ObjectId字段用于关联查询
            {
                '$addFields': {
                    'ai_generated_material_oid': {'$toObjectId': '$ai_generated_material_id'},
                    'promotion_task_oid': {'$toObjectId': '$promotion_task_id'}
                }
            },

            # 3. 关联AI生成素材文档
            {
                '$lookup': {
                    'from': 'ai_generated_material',
                    'localField': 'ai_generated_material_oid',
                    'foreignField': '_id',
                    'as': 'material_docs'
                }
            },
            {'$unwind': '$material_docs'},

            # 4. 过滤未删除的素材
            {'$match': {'material_docs.is_deleted': {'$ne': True}}},

            # 5. 关联推广任务文档
            {
                '$lookup': {
                    'from': 'promotion_task',
                    'localField': 'promotion_task_oid',
                    'foreignField': '_id',
                    'as': 'promotion_task_docs'
                }
            },
            {'$unwind': '$promotion_task_docs'},

            # 6. 过滤未删除且匹配平台的任务
            {
                '$match': {
                    'promotion_task_docs.is_deleted': {'$ne': True},
                    'promotion_task_docs.platform': preferred_platform
                }
            },

            # 7. 添加产品ObjectId字段
            {
                '$addFields': {
                    'product_oid': {
                        '$cond': [
                            {
                                '$and': [
                                    {'$ne': ['$promotion_task_docs.product_id', None]},
                                    {'$ne': ['$promotion_task_docs.product_id', ""]}
                                ]
                            },
                            {'$toObjectId': '$promotion_task_docs.product_id'},
                            None
                        ]
                    }
                }
            },

            # 8. 关联产品文档
            {
                '$lookup': {
                    'from': 'product',
                    'localField': 'product_oid',
                    'foreignField': '_id',
                    'as': 'product_docs'
                }
            },
            {'$unwind': {'path': '$product_docs', 'preserveNullAndEmptyArrays': True}},

            # 9. 添加领域信息和优先级标记
            {
                '$addFields': {
                    'domain': {'$ifNull': ['$product_docs.domain', ['未知领域']]},
                    'isPrioritized': {'$cond': [{'$in': [preferred_domain, {'$ifNull': ['$product_docs.domain', []]}]}, True, False]}
                }
            },

            # 10. 按推广任务分组(去重)
            {
                '$group': {
                    '_id': '$promotion_task_oid',
                    'representative_doc': {'$first': '$$ROOT'}
                }
            },

            # 11. 替换根文档
            {'$replaceRoot': {'newRoot': '$$ROOT.representative_doc'}},

            # 12. 排序: 优先显示匹配领域的任务，然后按创建时间倒序
            {'$sort': {'isPrioritized': -1, 'promotion_task_docs.create_at': -1}},
        ]

        facet_pipeline = base_pipeline + [
            {
                '$facet': {
                    'metadata': [{'$count': 'total'}],
                    'results': [{'$skip': (page - 1) * page_size}, {'$limit': page_size}]
                }
            }
        ]

        result = await PromotionTaskDetail.aggregate(facet_pipeline).to_list()

        if not result or not result[0].get('metadata'):
            return PageResponse(page=page, page_size=page_size, total=0, results=[])

        tasks_data = result[0]['results']

        formatted_tasks = []
        for task_data in tasks_data:
            image_url = None
            material_images = task_data.get('material_docs', {}).get('images', [])
            if material_images:
                sorted_images = sorted(material_images, key=lambda i: i.get('order', float('inf')))
                # 只处理第一张图片
                if sorted_images:
                    first_img = sorted_images[0]
                    if oss_key := first_img.get('oss_key'):
                        image_url = await oss_client.signed_get_url(oss_key)
                    else:
                        olog.warning(f"任务 {task_data.get('id_', '未知ID')} 的素材图片缺少oss_key，跳过该图片。")

            deadline_ts = task_data.get('promotion_task_docs', {}).get('end_date')
            deadline_str = datetime.fromtimestamp(deadline_ts).strftime(
                "%Y-%m-%d %H:%M:%S") if deadline_ts else 'N/A'

            formatted_tasks.append({
                '_id': str(task_data['_id']),
                'title': task_data['material_docs'].get('title', ''),
                'content': task_data['material_docs'].get('content', ''),

                'image_url': image_url,
                'domain': task_data.get('domain', ['未知领域']),
                'platform': task_data.get('promotion_task_docs', {}).get('platform', '未知平台'),
                'deadline': deadline_str,
                'isPrioritized': task_data.get('isPrioritized', False),
            })

        total = result[0]['metadata'][0]['total'] if result[0]['metadata'] else 0

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total,
            results=formatted_tasks,
        )

    @auth_required(['creator', 'admin'])
    async def query_promotion_task_detail(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询推广任务详情，只包括PromotionTaskDetail和关联的AiGeneratedMaterial信息
        
        Args:
            data: 包含 promotion_task_detail_id 的请求数据
            
        Returns:
            包含任务详情的字典
        """
        promotion_task_detail_id = data.get('promotion_task_detail_id')
        task_detail_object_id = PydanticObjectId(promotion_task_detail_id)

        # 构建聚合管道：只关联素材信息
        aggregation_pipeline = [
            # 1. 匹配指定的任务详情
            {'$match': {'_id': task_detail_object_id}},
            
            # 2. 准备关联查询所需的ObjectId字段
            {
                '$addFields': {
                    'material_object_id': {'$toObjectId': '$ai_generated_material_id'}
                }
            },
            
            # 3. 关联AI生成的素材信息
            {
                '$lookup': {
                    'from': 'ai_generated_material',
                    'localField': 'material_object_id',
                    'foreignField': '_id',
                    'as': 'material_info'
                }
            },
            {'$unwind': {'path': '$material_info', 'preserveNullAndEmptyArrays': True}},
            
            # 4. 投影最终需要的字段
            {
                '$project': {
                    '_id': 0,
                    'task_id': {'$toString': '$_id'},
                    'user_id': '$user_id',
                    'account_id': '$account_id',
                    'accepted_at': '$accepted_at',
                    'title': {'$ifNull': ['$material_info.title', '无标题']},
                    'content': {'$ifNull': ['$material_info.content', '']},
                    'material_images': {'$ifNull': ['$material_info.images', []]},
                    'publish_url': {'$ifNull': ['$publish_url', '']},
                    'publish_at': '$publish_at',
                    'validation_status': '$validation_status',
                    'validation_details': '$validation_details'
                }
            }
        ]

        # 执行聚合查询
        query_results = await PromotionTaskDetail.aggregate(aggregation_pipeline).to_list()

        # 验证查询结果
        if not query_results:
            olog.exception(f"尝试获取不存在的任务详情 (聚合): ID {promotion_task_detail_id}")
            raise MException("任务详情不存在或已被删除")

        # 获取任务数据
        task_detail_data = query_results[0]
        
        # 处理图片URL生成 - 生成所有图片的URL
        image_urls = []
        material_images = task_detail_data.get('material_images', [])
        
        if material_images:
            # 按order字段排序图片
            sorted_images = sorted(
                material_images, 
                key=lambda img: img.get('order', float('inf')) if isinstance(img, dict) else float('inf')
            )
            
            # 为所有图片生成签名URL
            for image in sorted_images:
                if isinstance(image, dict):
                    oss_key = image.get('oss_key')
                    if oss_key:
                        try:
                            image_url = await oss_client.signed_get_url(oss_key)
                            image_urls.append(image_url)
                        except Exception as e:
                            olog.warning(f"生成图片URL失败 (oss_key: {oss_key}): {str(e)}")
                            continue

        # 构造返回的任务详情
        task_detail_response = {
            '_id': task_detail_data['task_id'],
            'user_id': task_detail_data.get('user_id'),
            'account_id': task_detail_data.get('account_id'),
            'accepted_at': task_detail_data.get('accepted_at'),
            'title': task_detail_data['title'],
            'content': task_detail_data['content'],
            'image_url': image_urls[0] if image_urls else None,  # 保持向后兼容
            'image_urls': image_urls,  # 新增：返回所有图片URL
            'publish_url': task_detail_data.get('publish_url', ''),
            'publish_at': task_detail_data.get('publish_at'),
            'validation_status': task_detail_data.get('validation_status'),
            'validation_details': task_detail_data.get('validation_details')
        }

        return {'task_detail': task_detail_response}

    @auth_required(['creator', 'admin'])
    async def query_accepted_tasks(self, data: Dict[str, Any]) -> PageResponse[Dict[str, Any]]:
        user_id = data.get('user_id')
        account_id = data.get('account_id')
        page = data.get('page', 1)
        page_size = data.get('page_size', 5)

        match_conditions = {'user_id': str(user_id)}
        if account_id:
            account = await Account.find_one(Account.id == PydanticObjectId(account_id),
                                             Account.user_id == str(user_id))
            if not account:
                raise MException(f"账户 ID '{account_id}' 不存在或不属于您")
            match_conditions['account_id'] = str(account_id)

        total_count = await PromotionTaskDetail.find(match_conditions).count()

        if total_count == 0:
            return PageResponse(page=page, page_size=page_size, total=0, results=[])

        pipeline = [
            # 匹配条件
            {'$match': match_conditions},

            # 排序：按接受时间倒序，然后按ID倒序
            {'$sort': {'accepted_at': -1, '_id': -1}},

            # 分页
            {'$skip': (page - 1) * page_size},
            {'$limit': page_size},

            # 添加ObjectId字段用于关联查询
            {
                '$addFields': {
                    'ai_generated_material_oid': {'$toObjectId': '$ai_generated_material_id'}
                }
            },

            # 关联AI生成素材文档
            {
                '$lookup': {
                    'from': 'ai_generated_material',
                    'localField': 'ai_generated_material_oid',
                    'foreignField': '_id',
                    'as': 'material_docs'
                }
            },
            {'$unwind': '$material_docs'},

            # 投影最终结果 - 只保留PromotionTaskDetail和AiGeneratedMaterial相关信息
            {
                '$project': {
                    '_id': 0,
                    '_id': {'$toString': '$_id'},
                    'accepted_at': '$accepted_at',
                    'publish_url': {'$ifNull': ['$publish_url', '']},
                    'publish_at': '$publish_at',
                    'validation_status': '$validation_status',
                    'validation_details': '$validation_details',
                    'material_title': '$material_docs.title',
                    'material_content': '$material_docs.content',
                    'material_images': {'$ifNull': ['$material_docs.images', []]}
                }
            }
        ]
        aggregation_results = await PromotionTaskDetail.aggregate(pipeline).to_list()
        result_list = []

        for task_data in aggregation_results:
            image_url = None
            material_images_list = task_data.get('material_images', [])
            if material_images_list:
                sorted_material_images = sorted(material_images_list, key=lambda
                    img_info_lambda: img_info_lambda.get('order') if isinstance(img_info_lambda,
                                                                                dict) and 'order' in img_info_lambda else float(
                    'inf'))
                # 只处理第一张图片
                if sorted_material_images:
                    first_img = sorted_material_images[0]
                    oss_key = first_img.get('oss_key') if isinstance(first_img, dict) else None
                    if oss_key:
                        image_url = await oss_client.signed_get_url(oss_key)
                    else:
                        olog.warning(f"任务 {task_data.get('id_', '未知ID')} 的素材图片缺少oss_key，跳过该图片。")

            result_list.append({
                '_id': task_data['_id'],
                'accepted_at': task_data.get('accepted_at'),
                'title': task_data.get('material_title') or '无标题',
                'content': task_data.get('material_content') or '',
                'image_url': image_url,
                'publish_url': task_data.get('publish_url', ''),
                'publish_at': task_data.get('publish_at'),
                'validation_status': task_data.get('validation_status'),
                'validation_details': task_data.get('validation_details'),
            })

        return PageResponse(
            page=page,
            page_size=page_size,
            total=total_count,
            results=result_list
        )
