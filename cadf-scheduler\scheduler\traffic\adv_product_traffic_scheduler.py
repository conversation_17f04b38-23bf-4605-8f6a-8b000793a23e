import time

from models.models import Product, ProductTrafficMetrics
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    处理广告主产品流量统计
    """
    pipeline = [
        # 第一阶段：筛选未删除的产品
        {"$match": {"is_deleted": False}},

        # 第二阶段：关联推广任务表，获取每个产品的推广任务
        {
            "$lookup": {
                "from": "PromotionTask",  # 关联PromotionTask集合
                "let": {"product_id": {"$toString": "$_id"}},  # 将产品ID转为字符串格式
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$product_id", "$$product_id"]},  # 匹配产品ID
                                    {"$eq": ["$is_deleted", False]}  # 排除已删除的任务
                                ]
                            }
                        }
                    }
                ],
                "as": "tasks"  # 结果存储在tasks字段中
            }
        },

        # 第三阶段：过滤掉没有推广任务的产品
        {"$match": {"tasks": {"$ne": []}}},

        # 第四阶段：关联推广任务详情表，获取每个任务的详细数据
        {
            "$lookup": {
                "from": "PromotionTaskDetail",  # 关联PromotionTaskDetail集合
                "let": {
                    "task_ids": {
                        # 将tasks数组中的每个task._id转换为字符串格式
                        "$map": {
                            "input": "$tasks",
                            "as": "task",
                            "in": {"$toString": "$$task._id"}
                        }
                    }
                },
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                # 匹配promotion_task_id在task_ids数组中的记录
                                "$in": ["$promotion_task_id", "$$task_ids"]
                            }
                        }
                    }
                ],
                "as": "task_details"  # 结果存储在task_details字段中
            }
        },

        # 第五阶段：过滤掉没有推广任务详情的产品
        {"$match": {"task_details": {"$ne": []}}},

        # 第六阶段：构建任务ID到平台的映射对象
        {
            "$addFields": {
                "task_id_to_platform": {
                    # 将tasks数组转换为对象，key为task_id，value为platform
                    "$arrayToObject": {
                        "$map": {
                            "input": "$tasks",
                            "as": "task",
                            "in": {
                                "k": {"$toString": "$$task._id"},  # 键：任务ID（字符串格式）
                                "v": "$$task.platform"  # 值：平台名称
                            }
                        }
                    }
                }
            }
        },

        # 第七阶段：计算各项统计指标和平台信息
        {
            "$addFields": {
                # 计算总浏览量：遍历所有task_details，累加view_count
                "total_view_count": {
                    "$sum": {
                        "$map": {
                            "input": "$task_details",
                            "as": "detail",
                            "in": {"$ifNull": ["$$detail.view_count", 0]}  # 空值默认为0
                        }
                    }
                },

                # 计算总点赞量：遍历所有task_details，累加like_count
                "total_like_count": {
                    "$sum": {
                        "$map": {
                            "input": "$task_details",
                            "as": "detail",
                            "in": {"$ifNull": ["$$detail.like_count", 0]}
                        }
                    }
                },

                # 计算总评论量：遍历所有task_details，累加comment_count
                "total_comment_count": {
                    "$sum": {
                        "$map": {
                            "input": "$task_details",
                            "as": "detail",
                            "in": {"$ifNull": ["$$detail.comment_count", 0]}
                        }
                    }
                },

                # 计算总收藏量：遍历所有task_details，累加favorite_count
                "total_favorite_count": {
                    "$sum": {
                        "$map": {
                            "input": "$task_details",
                            "as": "detail",
                            "in": {"$ifNull": ["$$detail.favorite_count", 0]}
                        }
                    }
                },

                # 计算总分享量：遍历所有task_details，累加share_count
                "total_share_count": {
                    "$sum": {
                        "$map": {
                            "input": "$task_details",
                            "as": "detail",
                            "in": {"$ifNull": ["$$detail.share_count", 0]}
                        }
                    }
                },

                # 收集所有涉及的平台信息（去重）
                "platforms": {
                    "$setUnion": [
                        {
                            "$map": {
                                "input": "$task_details",
                                "as": "detail",
                                "in": {
                                    # 使用$let避免字段路径引用错误
                                    "$let": {
                                        "vars": {
                                            "task_id": "$$detail.promotion_task_id"  # 获取任务ID
                                        },
                                        "in": {
                                            # 根据任务ID从task_id_to_platform对象中获取对应的平台
                                            "$getField": {
                                                "field": "$$task_id",
                                                "input": "$task_id_to_platform"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        },

        # 第八阶段：投影最终需要的字段
        {
            "$project": {
                "_id": 1,  # 产品ID
                "user_id": 1,  # 用户ID
                "total_view_count": 1,  # 总浏览量
                "total_like_count": 1,  # 总点赞量
                "total_comment_count": 1,  # 总评论量
                "total_favorite_count": 1,  # 总收藏量
                "total_share_count": 1,  # 总分享量
                "platforms": {
                    # 过滤掉null值的平台
                    "$filter": {
                        "input": "$platforms",
                        "as": "platform",
                        "cond": {"$ne": ["$$platform", None]}
                    }
                }
            }
        }
    ]

    results = await Product.aggregate(pipeline).to_list()
    olog.info(f"发现 {len(results)} 个需要处理的产品。")

    for result in results:
        product_id_str: str = str(result["_id"])
        user_id_str: str = result["user_id"]
        traffic_data = {
            "platforms": result["platforms"],
            "total_view_count": result["total_view_count"],
            "total_like_count": result["total_like_count"],
            "total_comment_count": result["total_comment_count"],
            "total_favorite_count": result["total_favorite_count"],
            "total_share_count": result["total_share_count"],
            "last_updated_at": int(time.time())
        }
        await ProductTrafficMetrics.find_one(
            (ProductTrafficMetrics.user_id == user_id_str) &
            (ProductTrafficMetrics.product_id == product_id_str)
        ).upsert(
            {"$set": traffic_data},
            on_insert={"user_id": user_id_str, "product_id": product_id_str}
        )
    olog.info(f"产品 {product_id_str} 的流量统计数据已更新.")


@register_scheduler(trigger="cron", hour="*/2", minute="0")
class AdvTrafficStatisticsScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行广告主产品流量统计任务
        
        调度频率：每2小时执行一次
        """
        olog.info("开始执行广告主流量统计任务...")
        await execute_task()
        olog.info("广告主流量统计任务执行完毕.")


if __name__ == "__main__":
    import asyncio
    
    async def main():
        await init_models()
        await execute_task()
    
    asyncio.run(main())
