import time
from datetime import datetime
from typing import Dict, Any

from beanie import PydanticObjectId

from common_config.common_config import RedisKeyConfig
from models.models import Account, AccountTrafficMetrics, AiGeneratedMaterial, PromotionTaskDetail, ProductTrafficMetrics
from omni.log.log import olog
from omni.msg_queue.redis_set_consumer import consume_redis_set
from spider.xhs.xhs_account_traffic_metrics_spider import crawl_account_metrics


async def find_related_ids(title: str, publish_timestamp: int, account_id: str) -> Dict[str, str]:
    """
    根据account_id和publish_time查找相关的ID信息
    
    Args:
        title: 笔记标题
        publish_timestamp: 发布时间戳
        account_id: 账号ID
        platform: 平台名称
    
    Returns:
        Dict: 包含product_id, ai_generated_material_id, promotion_task_detail_id的字典
    """
    result = {
        "product_id": None,
        "ai_generated_material_id": None,
        "promotion_task_detail_id": None
    }
    
    # 使用管道查询合并两个查询逻辑
    time_diff = 24 * 60 * 60  # 24小时的秒数
    
    pipeline = [
        # 第一步：根据account_id和publish_time筛选PromotionTaskDetail
        {
            "$match": {
                "account_id": account_id,
                "publish_at": {
                    "$gte": publish_timestamp - time_diff,
                    "$lte": publish_timestamp + time_diff
                }
            }
        },
        # 第二步：关联AiGeneratedMaterial表
        {
            "$lookup": {
                "from": "ai_generated_materials",
                "let": {"material_id": "$ai_generated_material_id"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$_id", {"$toObjectId": "$$material_id"}]},
                                    {"$eq": ["$title", title]}
                                ]
                            }
                        }
                    }
                ],
                "as": "ai_material"
            }
        },
        # 第三步：只保留有匹配AiGeneratedMaterial的记录
        {
            "$match": {
                "ai_material": {"$ne": []}
            }
        },
        # 第四步：计算时间差并排序
        {
            "$addFields": {
                "time_diff": {
                    "$abs": {
                        "$subtract": ["$publish_at", publish_timestamp]
                    }
                }
            }
        },
        {
            "$sort": {"time_diff": 1}
        },
        # 第五步：只取最匹配的一条记录
        {
            "$limit": 1
        },
        # 第六步：格式化输出
        {
            "$project": {
                "promotion_task_detail_id": {"$toString": "$_id"},
                "ai_generated_material_id": {"$toString": {"$arrayElemAt": ["$ai_material._id", 0]}},
                "product_id": {"$arrayElemAt": ["$ai_material.product_id", 0]}
            }
        }
    ]
    
    results = await PromotionTaskDetail.aggregate(pipeline).to_list()
    
    if results:
        matched_result = results[0]
        result["promotion_task_detail_id"] = matched_result["promotion_task_detail_id"]
        result["ai_generated_material_id"] = matched_result["ai_generated_material_id"]
        result["product_id"] = matched_result["product_id"]
        olog.debug(f"找到匹配的记录: promotion_task_detail_id={result['promotion_task_detail_id']}, ai_generated_material_id={result['ai_generated_material_id']}")
    else:
        olog.debug(f"未找到匹配的记录，account_id: {account_id}, publish_time: {publish_timestamp}, title: {title}")
    
    return result


@consume_redis_set(redis_key=RedisKeyConfig.XHS_ACCOUNT_TRAFFIC_METRICS_SPIDER_SET, num_tasks=1)
async def handle_task(message_data: Dict[str, Any]) -> None:
    account_id = message_data.get("id_")
    olog.info(f"开始处理账号笔记指标爬取任务: {account_id}")

    account = await Account.find_one(
        Account.id == PydanticObjectId(account_id), 
        Account.status == "在线",
        Account.platform == "小红书"
    )
    
    notes_data, is_logged_in = await crawl_account_metrics(account.cookie)
    
    # 更新账号登录状态
    current_time = int(time.time())
    new_status = "在线" if is_logged_in else "离线"
    
    account.status = new_status
    account.last_login_check_at = current_time
    await account.save()
    olog.debug(f"账号 {account.name} 状态更新为: {new_status}")
    
    if not is_logged_in:
        olog.warning(f"账号 {account_id} 未登录，已更新状态为离线")
        return
    
    if not notes_data:
        olog.warning(f"账号 {account_id} 指标爬取失败，未获取到任何数据")
        return
    
    crawl_time = int(time.time())

    for note in notes_data:
        publish_timestamp = (
            int(datetime.fromisoformat(note.publish_date).timestamp()) 
            if note.publish_date else None
        )

        # 查找相关的ID信息
        related_ids = {}
        if note.title and publish_timestamp:
            related_ids = await find_related_ids(note.title, publish_timestamp, str(account.id))
        
        # 如果没有找到所有相关ID，说明不是需要的笔记，跳过处理
        if not (related_ids.get("promotion_task_detail_id") and 
                related_ids.get("ai_generated_material_id") and 
                related_ids.get("product_id")):
            olog.debug(f"跳过笔记 '{note.title}'，未找到完整的相关ID: promotion_task_detail_id={related_ids.get('promotion_task_detail_id')}, ai_generated_material_id={related_ids.get('ai_generated_material_id')}, product_id={related_ids.get('product_id')}")
            continue
        
        # 构建查询条件（包含所有ID）
        query_conditions = {
            "account_id": str(account.id),
            "platform": account.platform,
            "title": note.title,
            "product_id": related_ids.get("product_id"),
            "ai_generated_material_id": related_ids.get("ai_generated_material_id"),
            "promotion_task_detail_id": related_ids.get("promotion_task_detail_id")
        }
        
        # 查询是否存在
        existing_record = await AccountTrafficMetrics.find_one(query_conditions)
        
        if existing_record:
            # 更新已存在的记录
            await existing_record.update({
                "$set": {
                    "crawled_at": crawl_time,
                    "publish_time": publish_timestamp,
                    "view_count": note.views,
                    "like_count": note.likes,
                    "comment_count": note.comments,
                    "share_count": note.shares,
                    "favorite_count": note.favorites
                }
            })
        else:
            # 新增记录
            await AccountTrafficMetrics(
                account_id=str(account.id),
                product_id=related_ids.get("product_id"),
                ai_generated_material_id=related_ids.get("ai_generated_material_id"),
                promotion_task_detail_id=related_ids.get("promotion_task_detail_id"),
                platform=account.platform,
                crawled_at=crawl_time,
                title=note.title,
                publish_time=publish_timestamp,
                view_count=note.views,
                like_count=note.likes,
                comment_count=note.comments,
                share_count=note.shares,
                favorite_count=note.favorites
            ).insert()

    olog.info(f"成功为账号 {account_id} ({account.name}) 存储了 {len(notes_data)} 条指标数据")
